// Video Layout Components
export { default as VideoLayout, FONT_SIZES, VIDEO_COLORS } from './VideoLayout';
export { default as VideoTitle } from './VideoTitle';
export { default as VideoSubtitle } from './VideoSubtitle';
export { default as VideoBackground } from './VideoBackground';
export { default as VideoTransition } from './VideoTransition';
export { default as Video<PERSON>hart } from './VideoChart';
export { default as VideoEmphasis, VideoTextEmphasis } from './VideoEmphasis';
export { 
  default as VideoScene, 
  OpeningScene, 
  ContentScene, 
  DataScene, 
  ConclusionScene 
} from './VideoScene';

// Legacy components (if needed)
export { default as AnimatedText } from './AnimatedText';
export { default as DataChart } from './DataChart';

// Type definitions for video components
export interface VideoTheme {
  name: 'dark' | 'light' | 'tech' | 'nature' | 'corporate';
  colors: {
    text: string;
    overlay: string;
    accent: string;
  };
}

export interface ChartDataItem {
  label: string;
  value: number;
  color?: string;
}

export interface VideoSceneConfig {
  startFrame: number;
  durationInFrames: number;
  title: string;
  subtitle?: string;
  theme?: 'dark' | 'light' | 'tech' | 'nature' | 'corporate';
  backgroundType?: 'aurora' | 'particles' | 'waves' | 'grid' | 'galaxy' | 'plasma' | 'rays' | 'orb' | 'gradient' | 'solid';
}

// Utility functions for video creation
export const createSceneSequence = (scenes: VideoSceneConfig[]) => {
  let currentFrame = 0;
  return scenes.map(scene => ({
    ...scene,
    startFrame: currentFrame,
    endFrame: (currentFrame += scene.durationInFrames)
  }));
};

export const calculateTotalDuration = (scenes: VideoSceneConfig[]) => {
  return scenes.reduce((total, scene) => total + scene.durationInFrames, 0);
};

// Common animation timings (in frames at 30fps)
export const ANIMATION_TIMINGS = {
  QUICK: 15,      // 0.5 seconds
  NORMAL: 30,     // 1 second
  SLOW: 60,       // 2 seconds
  EMPHASIS: 90,   // 3 seconds
} as const;

// Common scene durations
export const SCENE_DURATIONS = {
  INTRO: 150,     // 5 seconds
  CONTENT: 300,   // 10 seconds
  TRANSITION: 60, // 2 seconds
  OUTRO: 180,     // 6 seconds
} as const;
