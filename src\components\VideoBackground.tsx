import React from 'react';
import { AbsoluteFill } from 'remotion';
import { Aurora } from './reactbits/backgrounds/Aurora';
import { Particles } from './reactbits/backgrounds/Particles';
import { Waves } from './reactbits/backgrounds/Waves';
import { GridMotion } from './reactbits/backgrounds/GridMotion';
import { Galaxy } from './reactbits/backgrounds/Galaxy';
import { Plasma } from './reactbits/backgrounds/Plasma';
import { LightRays } from './reactbits/backgrounds/LightRays';
import { Orb } from './reactbits/backgrounds/Orb';

interface VideoBackgroundProps {
  type?: 'aurora' | 'particles' | 'waves' | 'grid' | 'galaxy' | 'plasma' | 'rays' | 'orb' | 'gradient' | 'solid';
  theme?: 'dark' | 'light' | 'tech' | 'nature' | 'corporate';
  intensity?: 'low' | 'medium' | 'high';
  className?: string;
}

export const VideoBackground: React.FC<VideoBackgroundProps> = ({
  type = 'gradient',
  theme = 'dark',
  intensity = 'medium',
  className = ''
}) => {
  
  const getGradientBackground = () => {
    switch (theme) {
      case 'dark':
        return 'bg-gradient-to-br from-slate-900 via-slate-800 to-slate-900';
      case 'light':
        return 'bg-gradient-to-br from-gray-100 via-white to-gray-200';
      case 'tech':
        return 'bg-gradient-to-br from-blue-900 via-indigo-900 to-purple-900';
      case 'nature':
        return 'bg-gradient-to-br from-green-900 via-emerald-800 to-teal-900';
      case 'corporate':
        return 'bg-gradient-to-br from-gray-800 via-slate-700 to-gray-900';
      default:
        return 'bg-gradient-to-br from-slate-900 to-slate-800';
    }
  };
  
  const getSolidBackground = () => {
    switch (theme) {
      case 'dark':
        return 'bg-slate-900';
      case 'light':
        return 'bg-white';
      case 'tech':
        return 'bg-blue-900';
      case 'nature':
        return 'bg-green-900';
      case 'corporate':
        return 'bg-gray-800';
      default:
        return 'bg-slate-900';
    }
  };
  
  const renderBackground = () => {
    const baseProps = {
      className: className
    };
    
    switch (type) {
      case 'aurora':
        return (
          <Aurora 
            {...baseProps}
            intensity={intensity}
          />
        );
        
      case 'particles':
        return (
          <Particles 
            {...baseProps}
            density={intensity === 'low' ? 50 : intensity === 'medium' ? 100 : 150}
          />
        );
        
      case 'waves':
        return (
          <Waves 
            {...baseProps}
            amplitude={intensity === 'low' ? 0.3 : intensity === 'medium' ? 0.5 : 0.8}
          />
        );
        
      case 'grid':
        return (
          <GridMotion 
            {...baseProps}
            speed={intensity === 'low' ? 0.5 : intensity === 'medium' ? 1 : 1.5}
          />
        );
        
      case 'galaxy':
        return (
          <Galaxy 
            {...baseProps}
            starCount={intensity === 'low' ? 100 : intensity === 'medium' ? 200 : 300}
          />
        );
        
      case 'plasma':
        return (
          <Plasma 
            {...baseProps}
            intensity={intensity}
          />
        );
        
      case 'rays':
        return (
          <LightRays 
            {...baseProps}
            rayCount={intensity === 'low' ? 5 : intensity === 'medium' ? 8 : 12}
          />
        );
        
      case 'orb':
        return (
          <Orb 
            {...baseProps}
            size={intensity === 'low' ? 'small' : intensity === 'medium' ? 'medium' : 'large'}
          />
        );
        
      case 'gradient':
        return (
          <div className={`${getGradientBackground()} ${className}`} />
        );
        
      case 'solid':
        return (
          <div className={`${getSolidBackground()} ${className}`} />
        );
        
      default:
        return (
          <div className={`${getGradientBackground()} ${className}`} />
        );
    }
  };
  
  return (
    <AbsoluteFill>
      {renderBackground()}
    </AbsoluteFill>
  );
};

export default VideoBackground;
