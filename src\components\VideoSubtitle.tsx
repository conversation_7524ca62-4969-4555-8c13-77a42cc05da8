import React from 'react';
import { useCurrentFrame, interpolate } from 'remotion';
import { ScrollReveal } from './reactbits/textanimations/ScrollReveal';
import { FallingText } from './reactbits/textanimations/FallingText';
import { TextType } from './reactbits/textanimations/TextType';

interface VideoSubtitleProps {
  text: string;
  animation?: 'scroll' | 'falling' | 'typewriter' | 'fade';
  theme?: 'dark' | 'light';
  delay?: number;
  duration?: number;
  maxLines?: number;
  className?: string;
}

export const VideoSubtitle: React.FC<VideoSubtitleProps> = ({
  text,
  animation = 'fade',
  theme = 'dark',
  delay = 60,
  duration = 30,
  maxLines = 2,
  className = ''
}) => {
  const frame = useCurrentFrame();
  const opacity = interpolate(frame, [delay, delay + duration], [0, 1]);
  
  // Ensure text doesn't exceed line limits (15 chars per line)
  const formatText = (text: string, maxLines: number) => {
    const words = text.split(' ');
    const lines: string[] = [];
    let currentLine = '';
    
    for (const word of words) {
      if ((currentLine + ' ' + word).length <= 15) {
        currentLine = currentLine ? currentLine + ' ' + word : word;
      } else {
        if (currentLine) lines.push(currentLine);
        currentLine = word;
        if (lines.length >= maxLines) break;
      }
    }
    
    if (currentLine && lines.length < maxLines) {
      lines.push(currentLine);
    }
    
    return lines.join('\n');
  };
  
  const formattedText = formatText(text, maxLines);
  
  const baseClasses = `text-2xl md:text-3xl text-center leading-relaxed max-w-4xl ${className}`;
  const textColor = theme === 'dark' ? 'text-white' : 'text-black';
  const textShadow = theme === 'dark' 
    ? '2px 2px 4px rgba(0,0,0,0.8)' 
    : '2px 2px 4px rgba(255,255,255,0.8)';
  
  const commonProps = {
    className: `${baseClasses} ${textColor}`,
    style: { 
      opacity,
      textShadow
    }
  };
  
  switch (animation) {
    case 'scroll':
      return (
        <ScrollReveal 
          text={formattedText}
          {...commonProps}
        />
      );
      
    case 'falling':
      return (
        <FallingText 
          text={formattedText}
          {...commonProps}
        />
      );
      
    case 'typewriter':
      return (
        <TextType 
          text={formattedText}
          {...commonProps}
        />
      );
      
    case 'fade':
    default:
      return (
        <p {...commonProps}>
          {formattedText.split('\n').map((line, index) => (
            <React.Fragment key={index}>
              {line}
              {index < formattedText.split('\n').length - 1 && <br />}
            </React.Fragment>
          ))}
        </p>
      );
  }
};

export default VideoSubtitle;
