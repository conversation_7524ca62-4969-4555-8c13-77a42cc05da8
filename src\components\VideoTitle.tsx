import React from 'react';
import { useCurrentFrame, interpolate } from 'remotion';
import { GradientText } from './reactbits/textanimations/GradientText';
import { ShinyText } from './reactbits/textanimations/ShinyText';
import { TextType } from './reactbits/textanimations/TextType';
import { BlurText } from './reactbits/textanimations/BlurText';
import { DecryptedText } from './reactbits/textanimations/DecryptedText';

interface VideoTitleProps {
  text: string;
  animation?: 'gradient' | 'shiny' | 'typewriter' | 'blur' | 'decrypt' | 'fade';
  size?: 'large' | 'medium' | 'small';
  theme?: 'dark' | 'light';
  delay?: number;
  duration?: number;
  className?: string;
}

export const VideoTitle: React.FC<VideoTitleProps> = ({
  text,
  animation = 'gradient',
  size = 'large',
  theme = 'dark',
  delay = 0,
  duration = 30,
  className = ''
}) => {
  const frame = useCurrentFrame();
  const opacity = interpolate(frame, [delay, delay + duration], [0, 1]);
  
  // Size classes following video standards
  const sizeClasses = {
    large: 'text-5xl md:text-6xl', // 48px-64px
    medium: 'text-3xl md:text-4xl', // 36px-44px
    small: 'text-2xl md:text-3xl'   // 28px-36px
  };
  
  const baseClasses = `font-bold text-center ${sizeClasses[size]} ${className}`;
  const textColor = theme === 'dark' ? 'text-white' : 'text-black';
  
  const commonProps = {
    className: `${baseClasses} ${textColor}`,
    style: { opacity }
  };
  
  switch (animation) {
    case 'gradient':
      return (
        <GradientText 
          text={text}
          {...commonProps}
        />
      );
      
    case 'shiny':
      return (
        <ShinyText 
          text={text}
          {...commonProps}
        />
      );
      
    case 'typewriter':
      return (
        <TextType 
          text={text}
          {...commonProps}
        />
      );
      
    case 'blur':
      return (
        <BlurText 
          text={text}
          {...commonProps}
        />
      );
      
    case 'decrypt':
      return (
        <DecryptedText 
          text={text}
          {...commonProps}
        />
      );
      
    case 'fade':
    default:
      return (
        <h1 {...commonProps}>
          {text}
        </h1>
      );
  }
};

export default VideoTitle;
