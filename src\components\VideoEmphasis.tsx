import React from 'react';
import { useCurrentFrame, interpolate } from 'remotion';
import { GlareHover } from './reactbits/animotions/GlareHover';
import { ElectricBorder } from './reactbits/animotions/ElectricBorder';
import { StarBorder } from './reactbits/animotions/StarBorder';
import { ShinyText } from './reactbits/textanimations/ShinyText';
import { GradientText } from './reactbits/textanimations/GradientText';

interface VideoEmphasisProps {
  children: React.ReactNode;
  type?: 'glow' | 'electric' | 'star' | 'pulse' | 'scale' | 'highlight';
  duration?: number;
  delay?: number;
  intensity?: 'low' | 'medium' | 'high';
  color?: string;
  className?: string;
}

export const VideoEmphasis: React.FC<VideoEmphasisProps> = ({
  children,
  type = 'glow',
  duration = 60, // 1-2 seconds at 30fps
  delay = 0,
  intensity = 'medium',
  color = '#3B82F6',
  className = ''
}) => {
  const frame = useCurrentFrame();
  
  // Emphasis animation that appears briefly
  const emphasisProgress = interpolate(
    frame,
    [delay, delay + duration / 3, delay + (duration * 2) / 3, delay + duration],
    [0, 1, 1, 0],
    { extrapolateLeft: 'clamp', extrapolateRight: 'clamp' }
  );
  
  const getIntensityValue = () => {
    switch (intensity) {
      case 'low': return 0.3;
      case 'medium': return 0.6;
      case 'high': return 1.0;
      default: return 0.6;
    }
  };
  
  const intensityValue = getIntensityValue();
  
  const renderEmphasis = () => {
    switch (type) {
      case 'glow':
        return (
          <GlareHover className={className}>
            <div
              style={{
                filter: `drop-shadow(0 0 ${20 * intensityValue * emphasisProgress}px ${color})`,
                transform: `scale(${1 + 0.05 * intensityValue * emphasisProgress})`
              }}
            >
              {children}
            </div>
          </GlareHover>
        );
        
      case 'electric':
        return (
          <ElectricBorder 
            className={className}
            style={{ opacity: emphasisProgress }}
          >
            {children}
          </ElectricBorder>
        );
        
      case 'star':
        return (
          <StarBorder 
            className={className}
            style={{ opacity: emphasisProgress }}
          >
            {children}
          </StarBorder>
        );
        
      case 'pulse':
        const pulseScale = 1 + (0.1 * intensityValue * Math.sin(frame * 0.2) * emphasisProgress);
        return (
          <div
            className={className}
            style={{
              transform: `scale(${pulseScale})`,
              filter: `brightness(${1 + 0.3 * intensityValue * emphasisProgress})`
            }}
          >
            {children}
          </div>
        );
        
      case 'scale':
        const scaleValue = 1 + (0.2 * intensityValue * emphasisProgress);
        return (
          <div
            className={className}
            style={{
              transform: `scale(${scaleValue})`,
              transformOrigin: 'center'
            }}
          >
            {children}
          </div>
        );
        
      case 'highlight':
        return (
          <div
            className={className}
            style={{
              backgroundColor: `${color}${Math.floor(emphasisProgress * 255 * 0.3).toString(16).padStart(2, '0')}`,
              borderRadius: '8px',
              padding: `${4 * emphasisProgress}px ${8 * emphasisProgress}px`,
              border: `2px solid ${color}`,
              borderOpacity: emphasisProgress
            }}
          >
            {children}
          </div>
        );
        
      default:
        return (
          <div className={className}>
            {children}
          </div>
        );
    }
  };
  
  return renderEmphasis();
};

// Specialized text emphasis component
interface VideoTextEmphasisProps {
  text: string;
  type?: 'shiny' | 'gradient' | 'glow';
  duration?: number;
  delay?: number;
  className?: string;
}

export const VideoTextEmphasis: React.FC<VideoTextEmphasisProps> = ({
  text,
  type = 'shiny',
  duration = 60,
  delay = 0,
  className = ''
}) => {
  const frame = useCurrentFrame();
  const emphasisProgress = interpolate(
    frame,
    [delay, delay + duration],
    [0, 1],
    { extrapolateLeft: 'clamp', extrapolateRight: 'clamp' }
  );
  
  const commonProps = {
    text,
    className: `${className} font-bold`,
    style: { opacity: emphasisProgress }
  };
  
  switch (type) {
    case 'shiny':
      return <ShinyText {...commonProps} />;
    case 'gradient':
      return <GradientText {...commonProps} />;
    case 'glow':
      return (
        <div
          style={{
            filter: `drop-shadow(0 0 ${10 * emphasisProgress}px #3B82F6)`,
            opacity: emphasisProgress
          }}
        >
          <span className={`${className} font-bold text-white`}>
            {text}
          </span>
        </div>
      );
    default:
      return <ShinyText {...commonProps} />;
  }
};

export default VideoEmphasis;
