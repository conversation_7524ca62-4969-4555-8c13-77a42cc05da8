import React from 'react';
import { AbsoluteFill, useCurrentFrame, interpolate } from 'remotion';
import { GradientText } from './reactbits/textanimations/GradientText';
import { FadeContent } from './reactbits/animotions/FadeContent';
import { TextType } from './reactbits/textanimations/TextType';

// Font size constants following video standards
export const FONT_SIZES = {
  TITLE: '48px-64px',      // Main titles
  SUBTITLE: '36px-44px',   // Subtitles/emphasis
  BODY: '28px-36px'        // Body text/captions
} as const;

// Color palette for consistent theming
export const VIDEO_COLORS = {
  // Dark background themes
  DARK_BG: {
    text: '#FFFFFF',
    overlay: 'rgba(0, 0, 0, 0.6)',
    accent: '#3B82F6' // Blue accent
  },
  // Light background themes
  LIGHT_BG: {
    text: '#000000',
    overlay: 'rgba(255, 255, 255, 0.8)',
    accent: '#F97316' // Orange accent
  },
  // Emphasis colors
  EMPHASIS: {
    primary: '#3B82F6',   // Blue
    secondary: '#F97316', // Orange
    danger: '#EF4444'     // Red
  }
} as const;

interface VideoLayoutProps {
  title: string;
  subtitle?: string;
  children: React.ReactNode;
  theme?: 'dark' | 'light';
  titleAnimation?: 'gradient' | 'typewriter' | 'fade';
  backgroundComponent?: React.ReactNode;
}

export const VideoLayout: React.FC<VideoLayoutProps> = ({
  title,
  subtitle,
  children,
  theme = 'dark',
  titleAnimation = 'gradient',
  backgroundComponent
}) => {
  const frame = useCurrentFrame();
  const titleOpacity = interpolate(frame, [0, 30], [0, 1]);
  const subtitleOpacity = interpolate(frame, [60, 90], [0, 1]);
  
  const colors = theme === 'dark' ? VIDEO_COLORS.DARK_BG : VIDEO_COLORS.LIGHT_BG;
  
  const renderTitle = () => {
    const baseClasses = "text-5xl font-bold text-center px-4";
    
    switch (titleAnimation) {
      case 'gradient':
        return (
          <GradientText 
            text={title}
            className={baseClasses}
            style={{ opacity: titleOpacity, color: colors.text }}
          />
        );
      case 'typewriter':
        return (
          <TextType 
            text={title}
            className={`${baseClasses} text-white`}
            style={{ opacity: titleOpacity }}
          />
        );
      case 'fade':
      default:
        return (
          <h1 
            className={`${baseClasses} text-white`}
            style={{ opacity: titleOpacity }}
          >
            {title}
          </h1>
        );
    }
  };
  
  return (
    <AbsoluteFill>
      {/* Background */}
      {backgroundComponent || (
        <div className={`absolute inset-0 ${
          theme === 'dark' 
            ? 'bg-gradient-to-br from-slate-900 to-slate-800' 
            : 'bg-gradient-to-br from-gray-100 to-gray-200'
        }`} />
      )}
      
      {/* Title Area - Top 20% */}
      <div className="absolute top-0 left-0 right-0 h-1/5 flex items-center justify-center z-10">
        <div className="relative">
          {/* Semi-transparent background for title */}
          <div 
            className="absolute inset-0 rounded-lg"
            style={{ backgroundColor: colors.overlay }}
          />
          <div className="relative p-4">
            {renderTitle()}
          </div>
        </div>
      </div>
      
      {/* Main Content Area - Middle 60% with 10% safe margin */}
      <div className="absolute top-1/5 left-0 right-0 bottom-1/5 p-[10%] z-10">
        <FadeContent>
          {children}
        </FadeContent>
      </div>
      
      {/* Subtitle Area - Bottom 20% */}
      {subtitle && (
        <div className="absolute bottom-0 left-0 right-0 h-1/5 flex items-center justify-center z-10">
          <div className="relative max-w-4xl">
            {/* Semi-transparent background for subtitle */}
            <div 
              className="absolute inset-0 rounded-lg"
              style={{ backgroundColor: colors.overlay }}
            />
            <p 
              className="relative text-2xl text-center leading-relaxed p-4"
              style={{ 
                opacity: subtitleOpacity,
                color: colors.text,
                textShadow: theme === 'dark' ? '2px 2px 4px rgba(0,0,0,0.8)' : '2px 2px 4px rgba(255,255,255,0.8)'
              }}
            >
              {subtitle}
            </p>
          </div>
        </div>
      )}
    </AbsoluteFill>
  );
};

export default VideoLayout;
