import React from 'react';
import { useCurrentFrame, interpolate } from 'remotion';
import { PixelTransition } from './reactbits/animotions/PixelTransition';
import { FadeContent } from './reactbits/animotions/FadeContent';
import { GradualBlur } from './reactbits/animotions/GradualBlur';

interface VideoTransitionProps {
  children: React.ReactNode;
  type?: 'pixel' | 'fade' | 'blur' | 'dissolve' | 'push' | 'mask';
  direction?: 'in' | 'out' | 'both';
  startFrame?: number;
  duration?: number;
  className?: string;
}

export const VideoTransition: React.FC<VideoTransitionProps> = ({
  children,
  type = 'fade',
  direction = 'in',
  startFrame = 0,
  duration = 30,
  className = ''
}) => {
  const frame = useCurrentFrame();
  
  const getOpacity = () => {
    switch (direction) {
      case 'in':
        return interpolate(frame, [startFrame, startFrame + duration], [0, 1]);
      case 'out':
        return interpolate(frame, [startFrame, startFrame + duration], [1, 0]);
      case 'both':
        return interpolate(
          frame,
          [startFrame, startFrame + duration / 2, startFrame + duration],
          [0, 1, 0]
        );
      default:
        return 1;
    }
  };
  
  const opacity = getOpacity();
  
  const renderTransition = () => {
    switch (type) {
      case 'pixel':
        return (
          <PixelTransition className={className}>
            <div style={{ opacity }}>
              {children}
            </div>
          </PixelTransition>
        );
        
      case 'fade':
        return (
          <FadeContent className={className}>
            <div style={{ opacity }}>
              {children}
            </div>
          </FadeContent>
        );
        
      case 'blur':
        return (
          <GradualBlur className={className}>
            <div style={{ opacity }}>
              {children}
            </div>
          </GradualBlur>
        );
        
      case 'dissolve':
        return (
          <div 
            className={`transition-opacity duration-500 ${className}`}
            style={{ opacity }}
          >
            {children}
          </div>
        );
        
      case 'push':
        const translateX = interpolate(
          frame,
          [startFrame, startFrame + duration],
          direction === 'in' ? [-100, 0] : [0, 100],
          { extrapolateLeft: 'clamp', extrapolateRight: 'clamp' }
        );
        
        return (
          <div 
            className={className}
            style={{ 
              opacity,
              transform: `translateX(${translateX}%)` 
            }}
          >
            {children}
          </div>
        );
        
      case 'mask':
        const scale = interpolate(
          frame,
          [startFrame, startFrame + duration],
          direction === 'in' ? [0, 1] : [1, 0],
          { extrapolateLeft: 'clamp', extrapolateRight: 'clamp' }
        );
        
        return (
          <div 
            className={className}
            style={{ 
              opacity,
              transform: `scale(${scale})`,
              transformOrigin: 'center'
            }}
          >
            {children}
          </div>
        );
        
      default:
        return (
          <div className={className} style={{ opacity }}>
            {children}
          </div>
        );
    }
  };
  
  return renderTransition();
};

export default VideoTransition;
