import React from 'react';
import { useCurrentFrame, interpolate } from 'remotion';
import { Counter } from './reactbits/components/Counter';
import { AnimatedList } from './reactbits/components/AnimatedList';
import { SpotlightCard } from './reactbits/components/SpotlightCard';
import { GradientText } from './reactbits/textanimations/GradientText';

interface ChartDataItem {
  label: string;
  value: number;
  color?: string;
}

interface VideoChartProps {
  data: ChartDataItem[];
  type?: 'bar' | 'counter' | 'list' | 'highlight';
  title?: string;
  theme?: 'dark' | 'light';
  animationDelay?: number;
  animationDuration?: number;
  className?: string;
}

export const VideoChart: React.FC<VideoChartProps> = ({
  data,
  type = 'bar',
  title,
  theme = 'dark',
  animationDelay = 30,
  animationDuration = 60,
  className = ''
}) => {
  const frame = useCurrentFrame();
  const chartOpacity = interpolate(frame, [animationDelay, animationDelay + 30], [0, 1]);
  
  const colors = {
    primary: '#3B82F6',   // Blue
    secondary: '#F97316', // Orange
    success: '#10B981',   // Green
    warning: '#F59E0B',   // Yellow
    danger: '#EF4444',    // Red
    gray: '#6B7280'       // Gray
  };
  
  const defaultColors = [colors.primary, colors.secondary, colors.success, colors.warning, colors.danger];
  
  const renderBarChart = () => {
    const maxValue = Math.max(...data.map(item => item.value));
    
    return (
      <div className="space-y-4">
        {data.map((item, index) => {
          const barWidth = interpolate(
            frame,
            [animationDelay + index * 10, animationDelay + index * 10 + 30],
            [0, (item.value / maxValue) * 100],
            { extrapolateLeft: 'clamp', extrapolateRight: 'clamp' }
          );
          
          const itemColor = item.color || defaultColors[index % defaultColors.length];
          
          return (
            <div key={item.label} className="flex items-center space-x-4">
              <div className="w-24 text-right">
                <span className={`text-lg font-medium ${theme === 'dark' ? 'text-white' : 'text-black'}`}>
                  {item.label}
                </span>
              </div>
              <div className="flex-1 bg-gray-200 rounded-full h-8 relative overflow-hidden">
                <div
                  className="h-full rounded-full transition-all duration-500"
                  style={{
                    width: `${barWidth}%`,
                    backgroundColor: itemColor
                  }}
                />
                <div className="absolute inset-0 flex items-center justify-center">
                  <Counter
                    from={0}
                    to={item.value}
                    className={`text-sm font-bold ${theme === 'dark' ? 'text-white' : 'text-black'}`}
                  />
                </div>
              </div>
            </div>
          );
        })}
      </div>
    );
  };
  
  const renderCounterChart = () => {
    return (
      <div className="grid grid-cols-2 md:grid-cols-3 gap-6">
        {data.map((item, index) => {
          const itemColor = item.color || defaultColors[index % defaultColors.length];
          
          return (
            <SpotlightCard key={item.label} className="p-6 text-center">
              <div className="space-y-2">
                <Counter
                  from={0}
                  to={item.value}
                  className="text-4xl font-bold"
                  style={{ color: itemColor }}
                />
                <p className={`text-lg ${theme === 'dark' ? 'text-gray-300' : 'text-gray-700'}`}>
                  {item.label}
                </p>
              </div>
            </SpotlightCard>
          );
        })}
      </div>
    );
  };
  
  const renderListChart = () => {
    const listItems = data.map((item, index) => ({
      id: index,
      content: (
        <div className="flex justify-between items-center p-4 rounded-lg bg-opacity-20 bg-white">
          <span className={`text-lg font-medium ${theme === 'dark' ? 'text-white' : 'text-black'}`}>
            {item.label}
          </span>
          <Counter
            from={0}
            to={item.value}
            className="text-xl font-bold"
            style={{ color: item.color || defaultColors[index % defaultColors.length] }}
          />
        </div>
      )
    }));
    
    return (
      <AnimatedList
        items={listItems}
        className="space-y-3"
      />
    );
  };
  
  const renderHighlightChart = () => {
    const mainItem = data[0];
    const otherItems = data.slice(1);
    
    return (
      <div className="space-y-8">
        {/* Main highlight */}
        <div className="text-center">
          <SpotlightCard className="p-8 inline-block">
            <Counter
              from={0}
              to={mainItem.value}
              className="text-6xl font-bold"
              style={{ color: mainItem.color || colors.primary }}
            />
            <GradientText
              text={mainItem.label}
              className="text-2xl font-semibold mt-4"
            />
          </SpotlightCard>
        </div>
        
        {/* Supporting items */}
        {otherItems.length > 0 && (
          <div className="grid grid-cols-2 md:grid-cols-3 gap-4">
            {otherItems.map((item, index) => (
              <div key={item.label} className="text-center p-4 rounded-lg bg-opacity-10 bg-white">
                <Counter
                  from={0}
                  to={item.value}
                  className="text-2xl font-bold"
                  style={{ color: item.color || defaultColors[(index + 1) % defaultColors.length] }}
                />
                <p className={`text-sm mt-2 ${theme === 'dark' ? 'text-gray-300' : 'text-gray-700'}`}>
                  {item.label}
                </p>
              </div>
            ))}
          </div>
        )}
      </div>
    );
  };
  
  const renderChart = () => {
    switch (type) {
      case 'bar':
        return renderBarChart();
      case 'counter':
        return renderCounterChart();
      case 'list':
        return renderListChart();
      case 'highlight':
        return renderHighlightChart();
      default:
        return renderBarChart();
    }
  };
  
  return (
    <div className={`w-full ${className}`} style={{ opacity: chartOpacity }}>
      {title && (
        <div className="mb-8 text-center">
          <GradientText
            text={title}
            className="text-3xl font-bold"
          />
        </div>
      )}
      {renderChart()}
    </div>
  );
};

export default VideoChart;
