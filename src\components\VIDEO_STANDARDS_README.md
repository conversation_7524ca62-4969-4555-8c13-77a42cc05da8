# 🎬 视频排版标准组件库

基于你提供的视频排版标准，我们创建了一套完整的React组件库，充分利用react-bits组件来实现专业的视频制作效果。

## 📋 组件概览

### 核心布局组件
- **VideoLayout**: 基础视频布局容器，实现三段式布局（标题区/主体区/字幕区）
- **VideoScene**: 完整场景组件，包含预设的场景类型
- **VideoBackground**: 动态背景组件，支持多种react-bits背景效果

### 文字组件
- **VideoTitle**: 标题组件，支持多种动画效果（48-64px）
- **VideoSubtitle**: 字幕组件，自动控制行数和字数（28-36px）
- **VideoTextEmphasis**: 文字强调效果组件

### 数据展示组件
- **VideoChart**: 数据图表组件，支持柱状图、计数器、列表等
- **VideoEmphasis**: 内容强调组件，支持发光、电流、星星等效果

### 动画组件
- **VideoTransition**: 转场动画组件，支持像素、淡入、模糊等效果

## 🎨 设计规范实现

### 1. 屏幕布局标准
```typescript
// 三段式布局
<VideoLayout
  title="主标题"           // 上方20% - 标题区
  subtitle="字幕说明"      // 下方20% - 字幕区
  theme="dark"            // 主题配色
>
  {/* 中间60% - 主体内容区，自动10%安全边距 */}
  <YourContent />
</VideoLayout>
```

### 2. 文字规范
```typescript
// 标题 - 48-64px，加粗，居中
<VideoTitle 
  text="主标题" 
  size="large"           // 48-64px
  animation="gradient"   // 渐变动画
  theme="dark" 
/>

// 字幕 - 28-36px，最多两行，每行15字
<VideoSubtitle 
  text="字幕内容，自动控制行数" 
  maxLines={2}          // 最多2行
  animation="fade"      // 淡入动画
/>
```

### 3. 配色系统
```typescript
// 统一配色常量
import { VIDEO_COLORS } from '../components';

// 深色主题：白字 + 半透明黑底
VIDEO_COLORS.DARK_BG = {
  text: '#FFFFFF',
  overlay: 'rgba(0, 0, 0, 0.6)',
  accent: '#3B82F6'
}

// 浅色主题：黑字 + 半透明浅底
VIDEO_COLORS.LIGHT_BG = {
  text: '#000000', 
  overlay: 'rgba(255, 255, 255, 0.8)',
  accent: '#F97316'
}
```

### 4. 动效标准
```typescript
// 入场动效 - 0.3-0.5秒
<VideoTransition 
  type="fade"           // 淡入
  duration={30}         // 1秒 (30帧@30fps)
  direction="in"
>
  <Content />
</VideoTransition>

// 强调动效 - 1-2秒
<VideoEmphasis 
  type="glow"           // 发光效果
  duration={60}         // 2秒
  intensity="medium"
>
  <ImportantContent />
</VideoEmphasis>
```

## 🚀 快速开始

### 基础场景创建
```typescript
import { OpeningScene, ContentScene, DataScene, ConclusionScene } from '../components';

// 开场场景 - Aurora背景 + 渐变标题
<OpeningScene
  startFrame={0}
  durationInFrames={150}
  title="视频标题"
  subtitle="副标题说明"
  theme="dark"
  emphasizeTitle={true}
>
  <YourOpeningContent />
</OpeningScene>

// 内容场景 - 粒子背景 + 打字机效果
<ContentScene
  startFrame={150}
  durationInFrames={300}
  title="内容标题"
  subtitle="内容说明"
  backgroundType="particles"
  titleAnimation="typewriter"
>
  <YourMainContent />
</ContentScene>

// 数据场景 - 网格背景 + 解密效果
<DataScene
  startFrame={450}
  durationInFrames={300}
  title="数据分析"
  subtitle="图表展示"
  titleAnimation="decrypt"
>
  <VideoChart 
    data={chartData}
    type="bar"
    title="数据标题"
  />
</DataScene>
```

### 数据图表展示
```typescript
const chartData = [
  { label: '指标A', value: 85, color: '#3B82F6' },
  { label: '指标B', value: 92, color: '#10B981' },
  { label: '指标C', value: 78, color: '#F59E0B' }
];

// 柱状图
<VideoChart data={chartData} type="bar" />

// 计数器网格
<VideoChart data={chartData} type="counter" />

// 高亮展示
<VideoChart data={chartData} type="highlight" />
```

## 🎯 预设场景类型

### OpeningScene - 开场场景
- 背景：Aurora极光效果
- 标题：渐变动画 + 发光强调
- 适用：视频开头、重要介绍

### ContentScene - 内容场景  
- 背景：粒子效果
- 标题：打字机动画
- 适用：主要内容展示

### DataScene - 数据场景
- 背景：网格动效
- 标题：解密动画
- 主题：科技风格
- 适用：数据分析、图表展示

### ConclusionScene - 结尾场景
- 背景：波浪效果
- 标题：闪亮动画 + 星星强调
- 适用：总结、结尾

## 📊 React-bits组件映射

### 文字动画 (textanimations)
- `GradientText` → 彩色标题
- `TextType` → 打字机效果  
- `ShinyText` → 闪亮强调
- `BlurText` → 模糊揭示
- `DecryptedText` → 解密效果

### 背景效果 (backgrounds)
- `Aurora` → 现代动态背景
- `Particles` → 科技主题
- `Waves` → 平静流动场景
- `GridMotion` → 数据展示

### 动画效果 (animotions)
- `FadeContent` → 平滑内容转场
- `PixelTransition` → 像素转场
- `GlareHover` → 发光效果
- `ElectricBorder` → 电流边框

### UI组件 (components)
- `Counter` → 数字动画
- `SpotlightCard` → 聚光灯卡片
- `AnimatedList` → 动画列表

## 🔧 自定义配置

### 时间常量
```typescript
import { ANIMATION_TIMINGS, SCENE_DURATIONS } from '../components';

ANIMATION_TIMINGS.QUICK    // 15帧 (0.5秒)
ANIMATION_TIMINGS.NORMAL   // 30帧 (1秒)  
ANIMATION_TIMINGS.SLOW     // 60帧 (2秒)
ANIMATION_TIMINGS.EMPHASIS // 90帧 (3秒)

SCENE_DURATIONS.INTRO      // 150帧 (5秒)
SCENE_DURATIONS.CONTENT    // 300帧 (10秒)
SCENE_DURATIONS.TRANSITION // 60帧 (2秒)
SCENE_DURATIONS.OUTRO      // 180帧 (6秒)
```

### 主题配置
```typescript
// 支持的主题
'dark' | 'light' | 'tech' | 'nature' | 'corporate'

// 背景类型
'aurora' | 'particles' | 'waves' | 'grid' | 'galaxy' | 'plasma' | 'rays' | 'orb' | 'gradient' | 'solid'

// 动画类型
'gradient' | 'shiny' | 'typewriter' | 'blur' | 'decrypt' | 'fade'
```

## 📝 使用建议

1. **优先使用预设场景**：OpeningScene、ContentScene等已经配置好最佳实践
2. **遵循时间规范**：使用预定义的ANIMATION_TIMINGS和SCENE_DURATIONS
3. **保持配色一致**：使用VIDEO_COLORS常量确保主题统一
4. **控制文字长度**：字幕自动控制在2行内，每行15字左右
5. **合理使用强调**：强调效果持续1-2秒，避免过度使用

这套组件库完全遵循你提供的视频排版标准，充分利用react-bits的优化组件，确保专业的视频制作效果。
