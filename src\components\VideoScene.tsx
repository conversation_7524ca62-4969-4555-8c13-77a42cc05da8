import React from 'react';
import { AbsoluteFill, Sequence } from 'remotion';
import VideoLayout from './VideoLayout';
import VideoBackground from './VideoBackground';
import VideoTitle from './VideoTitle';
import VideoSubtitle from './VideoSubtitle';
import VideoTransition from './VideoTransition';
import VideoEmphasis from './VideoEmphasis';

interface VideoSceneProps {
  // Scene timing
  startFrame?: number;
  durationInFrames: number;
  
  // Content
  title: string;
  subtitle?: string;
  children?: React.ReactNode;
  
  // Visual styling
  theme?: 'dark' | 'light' | 'tech' | 'nature' | 'corporate';
  backgroundType?: 'aurora' | 'particles' | 'waves' | 'grid' | 'galaxy' | 'plasma' | 'rays' | 'orb' | 'gradient' | 'solid';
  backgroundIntensity?: 'low' | 'medium' | 'high';
  
  // Animations
  titleAnimation?: 'gradient' | 'shiny' | 'typewriter' | 'blur' | 'decrypt' | 'fade';
  subtitleAnimation?: 'scroll' | 'falling' | 'typewriter' | 'fade';
  transitionType?: 'pixel' | 'fade' | 'blur' | 'dissolve' | 'push' | 'mask';
  
  // Emphasis
  emphasizeTitle?: boolean;
  emphasisType?: 'glow' | 'electric' | 'star' | 'pulse' | 'scale' | 'highlight';
  emphasisDelay?: number;
  
  // Layout
  className?: string;
}

export const VideoScene: React.FC<VideoSceneProps> = ({
  startFrame = 0,
  durationInFrames,
  title,
  subtitle,
  children,
  theme = 'dark',
  backgroundType = 'gradient',
  backgroundIntensity = 'medium',
  titleAnimation = 'gradient',
  subtitleAnimation = 'fade',
  transitionType = 'fade',
  emphasizeTitle = false,
  emphasisType = 'glow',
  emphasisDelay = 60,
  className = ''
}) => {
  
  const renderTitle = () => {
    const titleComponent = (
      <VideoTitle
        text={title}
        animation={titleAnimation}
        theme={theme}
        size="large"
        delay={0}
        duration={30}
      />
    );
    
    if (emphasizeTitle) {
      return (
        <VideoEmphasis
          type={emphasisType}
          delay={emphasisDelay}
          duration={60}
          intensity="medium"
        >
          {titleComponent}
        </VideoEmphasis>
      );
    }
    
    return titleComponent;
  };
  
  return (
    <Sequence from={startFrame} durationInFrames={durationInFrames}>
      <VideoTransition
        type={transitionType}
        direction="in"
        startFrame={0}
        duration={30}
      >
        <VideoLayout
          title=""
          subtitle=""
          theme={theme}
          backgroundComponent={
            <VideoBackground
              type={backgroundType}
              theme={theme}
              intensity={backgroundIntensity}
            />
          }
        >
          <AbsoluteFill className={className}>
            {/* Title Area - Top 20% */}
            <div className="absolute top-0 left-0 right-0 h-1/5 flex items-center justify-center z-20">
              <div className="relative">
                {/* Semi-transparent background for title */}
                <div className="absolute inset-0 rounded-lg bg-black bg-opacity-40" />
                <div className="relative p-6">
                  {renderTitle()}
                </div>
              </div>
            </div>
            
            {/* Main Content Area - Middle 60% with 10% safe margin */}
            <div className="absolute top-1/5 left-0 right-0 bottom-1/5 p-[10%] z-20">
              <VideoTransition
                type="fade"
                direction="in"
                startFrame={30}
                duration={30}
              >
                {children}
              </VideoTransition>
            </div>
            
            {/* Subtitle Area - Bottom 20% */}
            {subtitle && (
              <div className="absolute bottom-0 left-0 right-0 h-1/5 flex items-center justify-center z-20">
                <div className="relative max-w-4xl">
                  {/* Semi-transparent background for subtitle */}
                  <div className="absolute inset-0 rounded-lg bg-black bg-opacity-40" />
                  <div className="relative p-4">
                    <VideoSubtitle
                      text={subtitle}
                      animation={subtitleAnimation}
                      theme={theme}
                      delay={60}
                      duration={30}
                      maxLines={2}
                    />
                  </div>
                </div>
              </div>
            )}
          </AbsoluteFill>
        </VideoLayout>
      </VideoTransition>
    </Sequence>
  );
};

// Preset scene configurations for common use cases
export const OpeningScene: React.FC<Omit<VideoSceneProps, 'backgroundType' | 'titleAnimation'>> = (props) => (
  <VideoScene
    {...props}
    backgroundType="aurora"
    titleAnimation="gradient"
    emphasizeTitle={true}
    emphasisType="glow"
  />
);

export const ContentScene: React.FC<Omit<VideoSceneProps, 'backgroundType' | 'titleAnimation'>> = (props) => (
  <VideoScene
    {...props}
    backgroundType="particles"
    titleAnimation="typewriter"
    transitionType="fade"
  />
);

export const DataScene: React.FC<Omit<VideoSceneProps, 'backgroundType' | 'theme'>> = (props) => (
  <VideoScene
    {...props}
    backgroundType="grid"
    theme="tech"
    titleAnimation="decrypt"
    transitionType="pixel"
  />
);

export const ConclusionScene: React.FC<Omit<VideoSceneProps, 'backgroundType' | 'titleAnimation'>> = (props) => (
  <VideoScene
    {...props}
    backgroundType="waves"
    titleAnimation="shiny"
    emphasizeTitle={true}
    emphasisType="star"
  />
);

export default VideoScene;
